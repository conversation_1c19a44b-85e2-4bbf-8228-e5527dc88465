/* Game Page Styles */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

.game-page {
    background-color: #0a0a0a;
    background-image:
        url('data:image/svg+xml;charset=utf8,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"%3E%3Cpath fill="%23111111" fill-opacity="0.8" d="M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,218.7C672,235,768,245,864,218.7C960,192,1056,128,1152,117.3C1248,107,1344,149,1392,170.7L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"%3E%3C/path%3E%3C/svg%3E'),
        radial-gradient(circle at 20% 30%, rgba(74, 0, 224, 0.15) 0%, rgba(0, 0, 0, 0) 50%),
        radial-gradient(circle at 80% 70%, rgba(138, 43, 226, 0.1) 0%, rgba(0, 0, 0, 0) 50%);
    background-size: cover, 100% 100%, 100% 100%;
    background-position: center bottom, center center, center center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    color: white;
    margin: 0;
    padding: 0;
    font-family: 'Poppins', sans-serif;
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

.game-page::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.8) 100%);
    pointer-events: none;
    z-index: -1;
}

.game-header {
    background: linear-gradient(90deg, #4a00e0, #8e2de2);
    color: white;
    text-align: center;
    padding: 25px 20px;
    font-size: 32px;
    font-weight: 700;
    text-transform: uppercase;
    position: relative;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
}

.game-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml;charset=utf8,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"%3E%3Cpath fill="%23ffffff" fill-opacity="0.1" d="M0,96L48,112C96,128,192,160,288,160C384,160,480,128,576,133.3C672,139,768,181,864,181.3C960,181,1056,139,1152,122.7C1248,107,1344,117,1392,122.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"%3E%3C/path%3E%3C/svg%3E');
    background-size: cover;
    background-position: center;
    opacity: 0.5;
    pointer-events: none;
}

.game-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px 15px;
    min-height: calc(100vh - 82px); /* 82px is header height */
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.game-content {
    background-color: rgba(20, 20, 20, 0.8);
    border-radius: 20px;
    padding: 30px 20px;
    text-align: center;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.05);
    max-height: 90vh;
    overflow: hidden;
}

.game-image-container {
    width: 120px;
    height: 120px;
    margin: 0 auto 20px;
    overflow: hidden;
    border-radius: 50%;
    background: linear-gradient(135deg, #4a00e0, #8e2de2);
    box-shadow: 0 10px 25px rgba(138, 43, 226, 0.3);
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.game-image-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
    z-index: 1;
}

#game-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

#game-subtitle {
    font-size: 22px;
    margin-bottom: 25px;
    font-weight: 600;
    background: linear-gradient(90deg, #4a00e0, #8e2de2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    display: inline-block;
}

.status-container {
    display: flex;
    justify-content: center;
    margin-bottom: 35px;
}

.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: stretch;
    background-color: rgba(30, 30, 30, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 0;
    backdrop-filter: blur(5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    width: 100%;
    max-width: 450px;
    overflow: hidden;
    margin: 0 auto;
}

.status-bar:hover {
    box-shadow: 0 8px 20px rgba(138, 43, 226, 0.2);
    border-color: rgba(138, 43, 226, 0.3);
}

.status-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 12px 15px;
    flex: 1;
    position: relative;
    transition: all 0.3s ease;
    border-right: 1px solid rgba(255, 255, 255, 0.05);
}

.status-item:last-child {
    border-right: none;
}

.status-item:hover {
    background-color: rgba(74, 0, 224, 0.1);
}

.status-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    margin-right: 10px;
    color: #4a00e0;
    font-size: 14px;
    background-color: rgba(74, 0, 224, 0.1);
    border-radius: 50%;
    padding: 5px;
}

.status-indicator {
    color: #00ff66;
    animation: pulse-online 2s infinite;
}

@keyframes pulse-online {
    0% { opacity: 0.7; }
    50% { opacity: 1; }
    100% { opacity: 0.7; }
}

.status-content {
    display: flex;
    flex-direction: column;
}

.status-label {
    font-size: 10px;
    color: #aaa;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 500;
    margin-bottom: 2px;
}

.status-value {
    font-weight: 600;
    font-size: 12px;
    transition: color 0.3s ease;
    white-space: nowrap;
}

.status-value.online {
    color: #00ff66;
    text-shadow: 0 0 10px rgba(0, 255, 102, 0.5);
}

/* Responsive styles for status bar */
@media (max-width: 480px) {
    .status-bar {
        flex-direction: row;
        border-radius: 10px;
        flex-wrap: nowrap;
        justify-content: space-between;
        padding: 8px 10px;
    }

    .status-item {
        border-right: none;
        border-bottom: none;
        padding: 5px;
        flex: 1;
        min-width: 0;
        justify-content: center;
    }

    .status-icon {
        margin-right: 3px;
        width: 16px;
        height: 16px;
        font-size: 10px;
        padding: 3px;
    }

    .status-content {
        align-items: center;
        text-align: center;
    }

    .status-label {
        font-size: 7px;
        margin-bottom: 1px;
        letter-spacing: 0.5px;
    }

    .status-value {
        font-size: 9px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
        font-weight: 700;
    }
}

.connection-form {
    background-color: rgba(255, 255, 255, 0.95);
    color: #333;
    border-radius: 20px;
    padding: 25px 20px;
    margin-top: 20px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.connection-form:hover {
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    transform: translateY(-3px);
}

.connection-form h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 20px;
    font-weight: 600;
    color: #4a00e0;
    position: relative;
    display: inline-block;
}

.connection-form h3::after {
    content: '';
    position: absolute;
    bottom: -6px;
    left: 0;
    width: 40px;
    height: 2px;
    background: linear-gradient(90deg, #4a00e0, #8e2de2);
    border-radius: 2px;
}

.input-instruction {
    color: #666;
    margin-bottom: 20px;
    font-size: 14px;
    line-height: 1.5;
}

.input-container {
    position: relative;
    margin-bottom: 20px;
}

.user-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    width: 18px;
    height: 18px;
    opacity: 0.4;
    transition: all 0.3s ease;
}

#username-input, #userid-input {
    width: 100%;
    padding: 14px 15px 14px 45px;
    border-radius: 25px;
    border: 2px solid #eee;
    background-color: #f9f9f9;
    font-size: 15px;
    box-sizing: border-box;
    transition: all 0.3s ease;
    font-family: 'Poppins', sans-serif;
}

#username-input:focus, #userid-input:focus {
    border-color: #4a00e0;
    background-color: #fff;
    box-shadow: 0 5px 15px rgba(74, 0, 224, 0.1);
    outline: none;
}

.input-container:focus-within .user-icon {
    opacity: 1;
    color: #4a00e0;
}

#connect-btn {
    background: linear-gradient(90deg, #4a00e0, #8e2de2);
    color: white;
    border: none;
    border-radius: 25px;
    padding: 14px 30px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(74, 0, 224, 0.3);
    position: relative;
    overflow: hidden;
    letter-spacing: 0.5px;
    font-family: 'Poppins', sans-serif;
}

#connect-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
    transition: left 0.7s ease;
}

#connect-btn:hover {
    background: linear-gradient(90deg, #3a00c0, #7e1dd2);
    box-shadow: 0 8px 20px rgba(74, 0, 224, 0.5);
    transform: translateY(-2px);
}

#connect-btn:hover::before {
    left: 100%;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.loading-content {
    background-color: #111;
    background-image: linear-gradient(to bottom, rgba(30, 30, 30, 0.9), rgba(15, 15, 15, 0.95));
    border-radius: 15px;
    padding: 25px 20px;
    text-align: center;
    max-width: 400px;
    width: 90%;
    box-shadow: 0 0 40px rgba(74, 0, 224, 0.2);
    border: 1px solid rgba(74, 0, 224, 0.1);
    position: relative;
    overflow: hidden;
}

.loading-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml;charset=utf8,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"%3E%3Cpath fill="%234a00e0" fill-opacity="0.05" d="M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,218.7C672,235,768,245,864,218.7C960,192,1056,128,1152,117.3C1248,107,1344,149,1392,170.7L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"%3E%3C/path%3E%3C/svg%3E');
    background-size: cover;
    background-position: center bottom;
    opacity: 0.3;
    pointer-events: none;
    z-index: -1;
}

.loading-header {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

.loading-icon {
    font-size: 20px;
    color: #4a00e0;
    margin-right: 12px;
    background-color: rgba(74, 0, 224, 0.1);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0 20px rgba(74, 0, 224, 0.3);
    animation: pulse-icon 2s infinite;
}

@keyframes pulse-icon {
    0% { transform: scale(1); box-shadow: 0 0 20px rgba(74, 0, 224, 0.3); }
    50% { transform: scale(1.05); box-shadow: 0 0 30px rgba(74, 0, 224, 0.5); }
    100% { transform: scale(1); box-shadow: 0 0 20px rgba(74, 0, 224, 0.3); }
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    border-top-color: #4a00e0;
    margin: 0 auto 15px;
    animation: spin 1s linear infinite;
    box-shadow: 0 0 20px rgba(74, 0, 224, 0.3);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 18px;
    margin-bottom: 20px;
    color: #fff;
    font-weight: 600;
    letter-spacing: 0.5px;
    animation: flicker 2s infinite;
    text-shadow: 0 0 10px rgba(74, 0, 224, 0.5);
}

@keyframes flicker {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

.progress-container {
    background-color: rgba(30, 30, 30, 0.8);
    border-radius: 20px;
    height: 10px;
    margin-bottom: 20px;
    overflow: hidden;
    position: relative;
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(50, 50, 50, 0.5);
}

.progress-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-bar {
    background: linear-gradient(90deg, #4a00e0, #8e2de2);
    height: 100%;
    width: 0%;
    transition: width 0.5s;
    box-shadow: 0 0 15px rgba(74, 0, 224, 0.5);
    position: relative;
    z-index: 1;
}

.progress-percentage {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #fff;
    font-size: 9px;
    font-weight: 600;
    text-shadow: 0 0 5px rgba(0, 0, 0, 0.8);
    z-index: 2;
}

.loading-status-container {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    background-color: rgba(30, 30, 30, 0.5);
    border-radius: 10px;
    padding: 12px;
    text-align: left;
    border: 1px solid rgba(50, 50, 50, 0.5);
}

.status-icon {
    font-size: 16px;
    color: #4a00e0;
    margin-right: 12px;
    margin-top: 2px;
}

.status-text {
    flex: 1;
}

.loading-status {
    font-size: 14px;
    color: #fff;
    font-weight: 600;
    margin-bottom: 4px;
}

.loading-details {
    font-size: 12px;
    color: #aaa;
    font-style: italic;
}

.loading-logs {
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    padding: 12px;
    text-align: left;
    border: 1px solid rgba(50, 50, 50, 0.5);
    max-height: 100px;
    overflow-y: auto;
}

.log-title {
    color: #aaa;
    font-size: 11px;
    text-transform: uppercase;
    margin-bottom: 8px;
    font-weight: 600;
    letter-spacing: 1px;
}

.log-content {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #4a00e0;
}

.log-entry {
    margin-bottom: 4px;
    color: #8e2de2;
    text-shadow: 0 0 5px rgba(142, 45, 226, 0.3);
}

/* Currency Selection Overlay */
.currency-selection-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.currency-selection-content {
    background-color: #111;
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    max-width: 450px;
    width: 90%;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    color: white;
    position: relative;
    overflow: hidden;
}

.currency-selection-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, rgba(74, 0, 224, 0.2) 0%, rgba(0, 0, 0, 0) 70%);
    z-index: -1;
}

.user-profile {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
    position: relative;
}

.user-avatar {
    width: 70px;
    height: 70px;
    margin-bottom: 12px;
    position: relative;
    border-radius: 50%;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, #4a00e0, #8e2de2);
    padding: 3px;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: white;
}

.username-display {
    text-align: center;
}

.username-label {
    font-size: 12px;
    color: #aaa;
    margin-bottom: 3px;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 500;
}

.username-value {
    font-size: 22px;
    font-weight: 700;
    color: #fff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.currency-selection-title {
    font-size: 20px;
    color: #4a00e0;
    margin-bottom: 12px;
    font-weight: 700;
    position: relative;
    display: inline-block;
    text-shadow: 0 0 10px rgba(74, 0, 224, 0.5);
}

.currency-selection-title::after {
    content: '';
    position: absolute;
    bottom: -6px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 2px;
    background: linear-gradient(90deg, #4a00e0, #8e2de2);
    border-radius: 2px;
    box-shadow: 0 0 10px rgba(74, 0, 224, 0.5);
}

.selection-instruction {
    color: #ccc;
    margin-bottom: 20px;
    font-size: 14px;
    line-height: 1.5;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.currency-options {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 12px;
    max-width: 100%;
}

.currency-option {
    background-color: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 15px 10px;
    min-width: 120px;
    max-width: 150px;
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.currency-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #4a00e0, #8e2de2);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.currency-option:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(74, 0, 224, 0.3);
    border-color: rgba(74, 0, 224, 0.5);
    background-color: rgba(74, 0, 224, 0.1);
}

.currency-option:hover::before {
    opacity: 1;
}

.currency-option.selected {
    border-color: #4a00e0;
    background-color: rgba(74, 0, 224, 0.2);
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(74, 0, 224, 0.4);
}

.currency-option.selected::before {
    opacity: 1;
}

.currency-icon {
    width: 50px;
    height: 50px;
    margin-bottom: 12px;
    transition: all 0.3s ease;
    filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.1));
}

.currency-option:hover .currency-icon {
    transform: scale(1.05);
    filter: drop-shadow(0 5px 10px rgba(74, 0, 224, 0.2));
}

.currency-icon img {
    width: 100%;
    height: 100%;
}

.currency-amount {
    font-size: 22px;
    font-weight: 700;
    color: #fff;
    margin-bottom: 5px;
    transition: color 0.3s ease;
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
}

.currency-option:hover .currency-amount {
    color: #8e2de2;
}

.currency-name {
    font-size: 14px;
    color: #4a00e0;
    font-weight: 600;
    background-color: rgba(74, 0, 224, 0.2);
    padding: 4px 10px;
    border-radius: 15px;
    transition: all 0.3s ease;
    text-shadow: 0 0 5px rgba(74, 0, 224, 0.5);
}

/* Processing Overlay */
.processing-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.processing-content {
    background-color: #111;
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    max-width: 350px;
    width: 90%;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    color: white;
    position: relative;
    overflow: hidden;
}

.processing-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, rgba(74, 0, 224, 0.2) 0%, rgba(0, 0, 0, 0) 70%);
    z-index: -1;
}

.processing-icon {
    font-size: 40px;
    margin-bottom: 20px;
    color: #4a00e0;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.processing-status {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 25px;
    color: #fff;
    min-height: 27px;
}

.progress-container {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    height: 12px;
    margin-bottom: 25px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2) inset;
}

.progress-bar {
    height: 100%;
    width: 0;
    background-color: #4a00e0;
    background-image: linear-gradient(90deg, #4a00e0, #8e2de2);
    transition: width 0.3s ease, background-color 0.5s ease;
    box-shadow: 0 0 10px rgba(74, 0, 224, 0.5);
}

.step-indicator {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.step {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.step.active {
    background-color: #4a00e0;
    box-shadow: 0 0 10px #4a00e0;
    transform: scale(1.2);
}

.error-message-animation {
    animation: pulse-error 1s infinite;
    color: #e74c3c;
    font-weight: 700;
}

.success-message-animation {
    animation: pulse-success 1.5s infinite;
    color: #00c853;
    font-weight: 700;
    font-size: 22px;
    text-shadow: 0 0 15px rgba(0, 200, 83, 0.5);
    margin-bottom: 15px;
    padding: 15px;
    background-color: rgba(0, 200, 83, 0.1);
    border-radius: 10px;
    border-left: 4px solid #00c853;
    letter-spacing: 0.5px;
    text-transform: uppercase;
}

/* تأثيرات النجاح المتقدمة */
.success-animation {
    margin: 20px auto;
    text-align: center;
    animation: fadeInUp 0.6s ease-out;
}

.success-message {
    color: #00c853;
    font-weight: 700;
    margin-top: 15px;
    font-size: 16px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.checkmark-circle {
    width: 80px;
    height: 80px;
    position: relative;
    display: inline-block;
    vertical-align: top;
    margin: 0 auto;
}

.checkmark-circle .background {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: #00c853;
    position: absolute;
    animation: checkmarkFill 0.4s ease-in-out;
}

.checkmark-circle .checkmark {
    border-radius: 5px;
}

.checkmark-circle .checkmark.draw:after {
    animation-delay: 0.2s;
    animation-duration: 0.5s;
    animation-name: checkmark;
    animation-timing-function: ease;
    animation-fill-mode: forwards;
    transform-origin: 50% 50%;
    stroke-dasharray: 48;
    stroke-dashoffset: 48;
}

.checkmark-circle .checkmark:after {
    opacity: 1;
    height: 40px;
    width: 20px;
    transform-origin: left top;
    border-right: 8px solid white;
    border-top: 8px solid white;
    border-radius: 2px !important;
    content: '';
    left: 26px;
    top: 45px;
    position: absolute;
}

@keyframes checkmark {
    0% {
        height: 0;
        width: 0;
        opacity: 1;
    }
    20% {
        height: 0;
        width: 20px;
        opacity: 1;
    }
    40% {
        height: 40px;
        width: 20px;
        opacity: 1;
    }
    100% {
        height: 40px;
        width: 20px;
        opacity: 1;
    }
}

@keyframes checkmarkFill {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.success-flash {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 200, 83, 0.2);
    z-index: 9999;
    pointer-events: none;
    animation: flashAnimation 0.7s ease-out;
}

@keyframes flashAnimation {
    0% { opacity: 0; }
    20% { opacity: 1; }
    100% { opacity: 0; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fade-in {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse-error {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes pulse-success {
    0% { transform: scale(1); opacity: 0.8; }
    50% { transform: scale(1.05); opacity: 1; }
    100% { transform: scale(1); opacity: 0.8; }
}

/* Manual Verification Button */
.manual-verification {
    margin-top: 25px;
    text-align: center;
}

.verify-button {
    background: linear-gradient(90deg, #ff5722, #ff9800);
    color: white;
    border: none;
    border-radius: 25px;
    padding: 12px 30px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(255, 87, 34, 0.3);
    position: relative;
    overflow: hidden;
    letter-spacing: 0.5px;
    font-family: 'Poppins', sans-serif;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.verify-button i {
    margin-right: 10px;
    font-size: 18px;
}

.verify-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
    transition: left 0.7s ease;
}

.verify-button:hover {
    background: linear-gradient(90deg, #e64a19, #f57c00);
    box-shadow: 0 8px 20px rgba(255, 87, 34, 0.5);
    transform: translateY(-2px);
}

.verify-button:hover::before {
    left: 100%;
}

.verify-button:active {
    transform: translateY(1px);
    box-shadow: 0 3px 10px rgba(255, 87, 34, 0.3);
}

.verification-status-box {
    background-color: #f8f9fa;
    border-radius: 12px;
    padding: 18px;
    margin-bottom: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    border: 1px solid #eee;
}

.status-row {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    text-align: left;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.status-row:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.status-icon {
    font-size: 16px;
    margin-right: 15px;
    width: 32px;
    height: 32px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.status-icon.failed {
    color: #fff;
    background-color: #e74c3c;
    box-shadow: 0 4px 10px rgba(231, 76, 60, 0.2);
}

.status-icon.pending {
    color: #fff;
    background-color: #f39c12;
    box-shadow: 0 4px 10px rgba(243, 156, 18, 0.2);
}

.status-icon.info {
    color: #fff;
    background-color: #3498db;
    box-shadow: 0 4px 10px rgba(52, 152, 219, 0.2);
}

.status-icon.success {
    color: #fff;
    background-color: #2ecc71;
    box-shadow: 0 4px 10px rgba(46, 204, 113, 0.2);
}

.status-text {
    flex: 1;
}

.status-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 3px;
    font-size: 14px;
}

.status-description {
    color: #666;
    font-size: 12px;
    line-height: 1.5;
}

.transfer-id {
    font-family: 'Courier New', monospace;
    background-color: rgba(52, 152, 219, 0.1);
    padding: 2px 4px;
    border-radius: 3px;
    color: #3498db;
    font-weight: 600;
    font-size: 12px;
}

.verification-action {
    margin-bottom: 20px;
    position: relative;
}

#manual-verify-btn {
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 10px 15px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    width: 100%;
    font-family: 'Poppins', sans-serif;
}

#manual-verify-btn:hover {
    background-color: #2980b9;
}

.secure-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(46, 204, 113, 0.1);
    color: #2ecc71;
    font-size: 14px;
    padding: 8px 15px;
    border-radius: 20px;
    font-weight: 600;
}

.secure-badge i {
    margin-right: 5px;
}

.verification-footer {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.time-estimate {
    display: flex;
    align-items: center;
    color: #666;
    font-size: 14px;
    margin-bottom: 10px;
}

.time-estimate i {
    color: #f39c12;
    margin-right: 5px;
}

.verification-note {
    color: #777;
    font-size: 14px;
    font-style: italic;
    line-height: 1.5;
}

/* Last Step Overlay */
.last-step-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #f8f9fa;
    background-image: url('data:image/svg+xml;charset=utf8,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"%3E%3Cpath fill="%23e9ecef" fill-opacity="1" d="M0,160L48,170.7C96,181,192,203,288,202.7C384,203,480,181,576,186.7C672,192,768,224,864,218.7C960,213,1056,171,1152,149.3C1248,128,1344,128,1392,128L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"%3E%3C/path%3E%3C/svg%3E');
    background-size: cover;
    background-position: center bottom;
    background-repeat: no-repeat;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.last-step-content {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    max-width: 300px;
    width: 90%;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    position: relative;
}

.last-step-title {
    font-size: 18px;
    font-weight: 700;
    margin-top: 0;
    margin-bottom: 20px;
    color: #333;
}

.game-info-simple {
    margin-bottom: 20px;
    text-align: center;
}

.game-info-simple p {
    margin: 8px 0;
    color: #555;
    font-size: 14px;
}

.game-info-simple strong {
    color: #333;
    font-weight: 600;
}

.timer-simple {
    margin-bottom: 20px;
    color: #666;
    font-size: 14px;
}

.timer-simple span {
    color: #e74c3c;
    font-weight: 600;
}

.game-info-container {
    display: flex;
    align-items: center;
    background-color: #f8f9fa;
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    border: 1px solid #eee;
}

.game-logo-container {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    border-radius: 12px;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 8px 15px rgba(52, 152, 219, 0.2);
    margin-right: 15px;
}

#last-step-game-logo {
    width: 80%;
    height: auto;
    filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.2));
}

.game-info {
    flex: 1;
    text-align: left;
}

.last-step-info-box {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.last-step-info-box:last-child {
    margin-bottom: 0;
}

.info-icon {
    width: 24px;
    height: 24px;
    background-color: rgba(52, 152, 219, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    color: #3498db;
    font-size: 12px;
}

.info-content {
    flex: 1;
}

.info-label {
    color: #7f8c8d;
    font-size: 10px;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
    margin-bottom: 1px;
}

.info-value {
    color: #2c3e50;
    font-weight: 600;
    font-size: 14px;
}

.verification-status {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 25px;
    color: #3498db;
    font-weight: 600;
}

.status-icon {
    margin-right: 10px;
    font-size: 20px;
}

.timer-container {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    background-color: rgba(231, 76, 60, 0.1);
    border-radius: 8px;
    padding: 12px;
    border-left: 2px solid #e74c3c;
}

.timer-icon {
    color: #e74c3c;
    font-size: 16px;
    margin-right: 12px;
}

.timer-content {
    text-align: left;
}

.timer-label {
    color: #7f8c8d;
    font-size: 10px;
    margin-bottom: 3px;
}

.timer-value {
    color: #e74c3c;
    font-weight: 700;
    font-size: 16px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.verification-action {
    margin-bottom: 15px;
}

#verify-now-btn {
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 10px 15px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    width: 100%;
    font-family: 'Poppins', sans-serif;
}

#verify-now-btn:hover {
    background-color: #2980b9;
}

.secure-note {
    color: #7f8c8d;
    font-size: 13px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
}

.secure-note i {
    color: #2ecc71;
    margin-right: 5px;
}

.captcha-box {
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.captcha-header {
    display: flex;
    align-items: center;
    color: #555;
    font-weight: bold;
    margin-bottom: 10px;
    font-size: 14px;
}

.captcha-header i {
    margin-right: 8px;
    color: #4285f4;
}

.captcha-message {
    color: #666;
    font-size: 13px;
    margin-bottom: 15px;
}

.captcha-checkbox {
    display: flex;
    align-items: center;
    position: relative;
}

.captcha-checkbox input[type="checkbox"] {
    width: 20px;
    height: 20px;
    margin-right: 10px;
    cursor: pointer;
}

.captcha-checkbox label {
    color: #333;
    font-size: 14px;
    cursor: pointer;
}

.captcha-logo {
    position: absolute;
    right: 0;
    font-size: 12px;
    color: #999;
    display: flex;
    align-items: center;
}

.captcha-logo i {
    margin-right: 5px;
    color: #4285f4;
}

#verify-btn {
    background-color: #cccccc;
    color: white;
    border: none;
    border-radius: 30px;
    padding: 15px 40px;
    font-size: 18px;
    font-weight: bold;
    cursor: not-allowed;
    transition: all 0.3s;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    width: 100%;
}

#verify-btn.active {
    background-color: #ff0066;
    cursor: pointer;
    box-shadow: 0 5px 15px rgba(255, 0, 102, 0.3);
}

#verify-btn.active:hover {
    background-color: #e6005c;
    box-shadow: 0 5px 20px rgba(255, 0, 102, 0.5);
}

.verification-progress-message {
    color: #4285f4;
    font-weight: bold;
    margin: 20px 0;
    text-align: center;
    font-size: 16px;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 0.6; }
    50% { opacity: 1; }
    100% { opacity: 0.6; }
}

/* Success Overlay */
.success-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.success-content {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    max-width: 300px;
    width: 90%;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.success-content h2 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
    font-size: 18px;
}

.success-content p {
    margin-bottom: 20px;
    color: #666;
    font-size: 14px;
}

#selected-amount {
    font-weight: bold;
    color: #3498db;
}

#back-btn {
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 10px 15px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
}

#back-btn:hover {
    background-color: #2980b9;
}

/* Responsive Design */
@media (max-width: 600px) {
    .game-header {
        font-size: 22px;
        padding: 12px;
    }

    .status-container {
        width: 100%;
    }

    .status-bar {
        padding: 8px 12px;
        width: 100%;
        max-width: 100%;
        overflow-x: auto;
        justify-content: flex-start;
        border-radius: 12px;
    }

    .status-item {
        white-space: nowrap;
        padding: 0 6px;
    }

    .status-label {
        font-size: 9px;
        margin-right: 4px;
    }

    .status-value {
        font-size: 11px;
    }

    .status-separator {
        height: 12px;
    }

    /* تحسينات عامة للأجهزة المحمولة */
    .game-container {
        padding: 15px 10px;
    }

    .game-content {
        padding: 20px 15px;
    }

    /* تحسينات لشاشة توليد الموارد */
    .resource-generation-content,
    .verification-failed-content,
    .last-step-content,
    .currency-selection-content {
        padding: 20px 15px;
        max-width: 100%;
    }

    .resource-icon {
        width: 35px;
        height: 35px;
        font-size: 18px;
    }

    .resource-title {
        font-size: 18px;
    }

    .resource-info-panel {
        padding: 12px;
    }

    .resource-console {
        max-height: 80px;
    }

    .console-content {
        max-height: 80px;
        font-size: 10px;
        padding: 10px;
    }

    .console-line {
        margin-bottom: 3px;
    }

    /* تحسينات لشاشة فشل التحقق */
    .alert-icon {
        width: 60px;
        height: 60px;
        font-size: 25px;
        margin-bottom: 12px;
    }

    .alert-title {
        font-size: 18px;
        margin-bottom: 12px;
    }

    .verification-message {
        padding: 12px;
        margin-bottom: 15px;
    }

    .message-icon {
        font-size: 16px;
    }

    .message-content p {
        font-size: 12px;
    }

    .verification-status-box {
        padding: 15px 12px;
        margin-bottom: 15px;
    }

    .status-icon {
        width: 28px;
        height: 28px;
        font-size: 14px;
        margin-right: 10px;
    }

    .status-title {
        font-size: 13px;
    }

    .status-description {
        font-size: 11px;
    }

    /* تحسينات لشاشة الخطوة الأخيرة */
    .verification-progress {
        margin-bottom: 15px;
    }

    .step-number {
        width: 20px;
        height: 20px;
        font-size: 10px;
    }

    .step-label {
        font-size: 8px;
        max-width: 40px;
    }

    .game-info-container {
        padding: 12px;
    }

    .game-logo-container {
        width: 45px;
        height: 45px;
    }

    .info-icon {
        width: 20px;
        height: 20px;
        font-size: 10px;
    }

    .info-label {
        font-size: 9px;
    }

    .info-value {
        font-size: 12px;
    }

    .timer-container {
        padding: 10px;
    }

    .timer-icon {
        font-size: 14px;
    }

    .timer-value {
        font-size: 14px;
    }

    .connection-form {
        padding: 20px;
    }

    /* تحسينات لشاشة اختيار كمية العملة على الأجهزة المحمولة */
    .currency-options {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
        gap: 8px;
    }

    .currency-option {
        padding: 12px 8px;
        min-width: 100px;
        max-width: 120px;
        flex: 0 1 auto;
    }

    .currency-icon {
        width: 50px;
        height: 50px;
        margin-bottom: 10px;
    }

    .currency-amount {
        font-size: 24px;
    }

    .currency-name {
        font-size: 14px;
    }

    .user-avatar {
        width: 60px;
        height: 60px;
    }

    .username-value {
        font-size: 20px;
    }

    .currency-selection-title {
        font-size: 18px;
        margin-bottom: 20px;
    }

    /* تحسينات لشاشة التحقق اليدوي على الأجهزة المحمولة */
    .manual-verification-content {
        padding: 20px;
    }

    .error-icon {
        font-size: 40px;
        margin-bottom: 15px;
    }

    .manual-verification-content h2 {
        font-size: 20px;
        margin-bottom: 15px;
    }

    .verification-message {
        font-size: 14px;
        margin-bottom: 20px;
    }

    .verification-status {
        padding: 15px;
        margin-bottom: 20px;
    }

    .status-item {
        margin-bottom: 10px;
    }

    .status-icon {
        font-size: 16px;
        margin-right: 10px;
    }

    .verification-instruction {
        font-size: 14px;
        margin-bottom: 15px;
    }

    #verify-btn {
        padding: 12px 30px;
        font-size: 16px;
    }

    /* تحسينات لشاشة الخطوة الأخيرة على الأجهزة المحمولة */
    .last-step-title {
        font-size: 24px;
        margin-bottom: 15px;
    }

    .game-logo-container {
        width: 100px;
        height: 100px;
        margin-bottom: 15px;
    }

    .last-step-instruction {
        font-size: 14px;
        margin-bottom: 20px;
    }

    .last-step-info-box {
        padding: 12px;
        margin-bottom: 10px;
    }

    .info-value {
        font-size: 16px;
    }

    .currency-icon-small {
        width: 25px;
        height: 25px;
    }

    .timer-value {
        font-size: 14px;
    }

    #verify-now-btn {
        padding: 12px 0;
        font-size: 16px;
    }
}
