<?php
// تعريف ثابت للتحقق من الوصول
define('ADMIN_ACCESS', true);

// بدء جلسة
session_start();

// تضمين ملفات الإعدادات والوظائف
require_once 'config.php';
require_once 'functions.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

// الحصول على الإجراء المطلوب
$action = $_GET['action'] ?? 'list';
$gameId = $_GET['id'] ?? '';

// عنوان الصفحة
$pageTitle = 'Games Management';

// معالجة الإجراءات
switch ($action) {
    case 'add':
        $pageTitle = 'Add New Game';

        // معالجة نموذج إضافة لعبة جديدة
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // معالجة كميات العملة
            $currencyAmounts = [];
            if (!empty($_POST['currency_amounts'])) {
                $amounts = explode(',', $_POST['currency_amounts']);
                foreach ($amounts as $amount) {
                    $amount = trim($amount);
                    if (is_numeric($amount) && $amount > 0) {
                        $currencyAmounts[] = (int)$amount;
                    }
                }
            }
            // إذا لم يتم تحديد كميات، استخدم القيم الافتراضية
            if (empty($currencyAmounts)) {
                $currencyAmounts = [600, 1200, 1600];
            }

            $gameData = [
                'id' => $_POST['id'] ?? '',
                'name' => $_POST['name'] ?? '',
                'currency' => $_POST['currency'] ?? '',
                'backgroundColor' => $_POST['backgroundColor'] ?? '#1e88e5',
                'available' => isset($_POST['available']) && $_POST['available'] === 'on',
                'currencyAmounts' => $currencyAmounts,
                'cpa_script' => $_POST['cpa_script'] ?? '',
                'cpa_function' => $_POST['cpa_function'] ?? '',
                'last_update' => $_POST['last_update'] ?? ''
            ];

            // التحقق من البيانات المطلوبة
            if (empty($gameData['id']) || empty($gameData['name'])) {
                setAlert('Game ID and Name are required', 'danger');
            } else {
                // معالجة صورة اللعبة
                $imageUrl = $_POST['image_url'] ?? '';

                if (!empty($imageUrl)) {
                    // استخدام رابط الصورة
                    $validateResult = validateImageUrl($imageUrl);
                    if ($validateResult['success']) {
                        $gameData['image'] = $validateResult['relative_path'];
                    } else {
                        setAlert('Error validating image URL: ' . $validateResult['message'], 'danger');
                        $gameData['image'] = '../img/placeholder-game.svg';
                    }
                } else if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                    // تحميل ملف الصورة
                    $uploadResult = uploadFile($_FILES['image'], '', 'game');
                    if ($uploadResult['success']) {
                        $gameData['image'] = $uploadResult['relative_path'];
                    } else {
                        setAlert('Error uploading image: ' . $uploadResult['message'], 'danger');
                        $gameData['image'] = '../img/placeholder-game.svg';
                    }
                } else {
                    $gameData['image'] = '../img/placeholder-game.svg';
                }

                // معالجة أيقونة العملة
                $currencyIconUrl = $_POST['currency_icon_url'] ?? '';

                if (!empty($currencyIconUrl)) {
                    // استخدام رابط أيقونة العملة
                    $validateResult = validateImageUrl($currencyIconUrl);
                    if ($validateResult['success']) {
                        $gameData['currencyIcon'] = $validateResult['relative_path'];
                    } else {
                        setAlert('Error validating currency icon URL: ' . $validateResult['message'], 'danger');
                        $gameData['currencyIcon'] = '../img/currency/default-currency.png';
                    }
                } else if (isset($_FILES['currencyIcon']) && $_FILES['currencyIcon']['error'] === UPLOAD_ERR_OK) {
                    // تحميل ملف أيقونة العملة
                    $uploadResult = uploadFile($_FILES['currencyIcon'], '', 'currency');
                    if ($uploadResult['success']) {
                        $gameData['currencyIcon'] = $uploadResult['relative_path'];
                    } else {
                        setAlert('Error uploading currency icon: ' . $uploadResult['message'], 'danger');
                        $gameData['currencyIcon'] = '../img/currency/default-currency.png';
                    }
                } else {
                    $gameData['currencyIcon'] = '../img/currency/default-currency.png';
                }

                // إضافة اللعبة
                if (addGame($gameData)) {
                    setAlert('Game added successfully', 'success');
                    header('Location: games.php');
                    exit;
                } else {
                    setAlert('Error adding game. Game ID might already exist.', 'danger');
                }
            }
        }
        break;

    case 'edit':
        $pageTitle = 'Edit Game';

        // التحقق من وجود معرف اللعبة
        if (empty($gameId)) {
            setAlert('Game ID is required', 'danger');
            header('Location: games.php');
            exit;
        }

        // الحصول على بيانات اللعبة
        $game = getGameById($gameId);
        if (!$game) {
            setAlert('Game not found', 'danger');
            header('Location: games.php');
            exit;
        }

        // معالجة نموذج تحديث اللعبة
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // معالجة كميات العملة
            $currencyAmounts = [];
            if (!empty($_POST['currency_amounts'])) {
                $amounts = explode(',', $_POST['currency_amounts']);
                foreach ($amounts as $amount) {
                    $amount = trim($amount);
                    if (is_numeric($amount) && $amount > 0) {
                        $currencyAmounts[] = (int)$amount;
                    }
                }
            }
            // إذا لم يتم تحديد كميات، استخدم القيم الحالية أو الافتراضية
            if (empty($currencyAmounts)) {
                $currencyAmounts = $game['currencyAmounts'] ?? [600, 1200, 1600];
            }

            $gameData = [
                'id' => $gameId,
                'name' => $_POST['name'] ?? '',
                'currency' => $_POST['currency'] ?? '',
                'backgroundColor' => $_POST['backgroundColor'] ?? '#1e88e5',
                'available' => isset($_POST['available']) && $_POST['available'] === 'on',
                'image' => $game['image'] ?? '../img/placeholder-game.svg',
                'currencyIcon' => $game['currencyIcon'] ?? '../img/currency/default-currency.png',
                'currencyAmounts' => $currencyAmounts,
                'cpa_script' => $_POST['cpa_script'] ?? $game['cpa_script'] ?? '',
                'cpa_function' => $_POST['cpa_function'] ?? $game['cpa_function'] ?? '',
                'last_update' => $_POST['last_update'] ?? $game['last_update'] ?? ''
            ];

            // التحقق من البيانات المطلوبة
            if (empty($gameData['name'])) {
                setAlert('Game Name is required', 'danger');
            } else {
                // معالجة صورة اللعبة
                $imageUrl = $_POST['image_url'] ?? '';

                if (!empty($imageUrl)) {
                    // استخدام رابط الصورة
                    $validateResult = validateImageUrl($imageUrl);
                    if ($validateResult['success']) {
                        $gameData['image'] = $validateResult['relative_path'];
                    } else {
                        setAlert('Error validating image URL: ' . $validateResult['message'], 'danger');
                    }
                } else if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                    // تحميل ملف الصورة
                    $uploadResult = uploadFile($_FILES['image'], '', 'game');
                    if ($uploadResult['success']) {
                        $gameData['image'] = $uploadResult['relative_path'];
                    } else {
                        setAlert('Error uploading image: ' . $uploadResult['message'], 'danger');
                    }
                }

                // معالجة أيقونة العملة
                $currencyIconUrl = $_POST['currency_icon_url'] ?? '';

                if (!empty($currencyIconUrl)) {
                    // استخدام رابط أيقونة العملة
                    $validateResult = validateImageUrl($currencyIconUrl);
                    if ($validateResult['success']) {
                        $gameData['currencyIcon'] = $validateResult['relative_path'];
                    } else {
                        setAlert('Error validating currency icon URL: ' . $validateResult['message'], 'danger');
                    }
                } else if (isset($_FILES['currencyIcon']) && $_FILES['currencyIcon']['error'] === UPLOAD_ERR_OK) {
                    // تحميل ملف أيقونة العملة
                    $uploadResult = uploadFile($_FILES['currencyIcon'], '', 'currency');
                    if ($uploadResult['success']) {
                        $gameData['currencyIcon'] = $uploadResult['relative_path'];
                    } else {
                        setAlert('Error uploading currency icon: ' . $uploadResult['message'], 'danger');
                    }
                }

                // تحديث اللعبة
                if (updateGame($gameId, $gameData)) {
                    setAlert('Game updated successfully', 'success');
                    header('Location: games.php');
                    exit;
                } else {
                    setAlert('Error updating game', 'danger');
                }
            }
        }
        break;

    case 'delete':
        // التحقق من وجود معرف اللعبة
        if (empty($gameId)) {
            setAlert('Game ID is required', 'danger');
            header('Location: games.php');
            exit;
        }

        // حذف اللعبة
        if (deleteGame($gameId)) {
            setAlert('Game deleted successfully', 'success');
        } else {
            setAlert('Error deleting game', 'danger');
        }

        header('Location: games.php');
        exit;
        break;
}

// تضمين ملف الرأس
include 'header.php';

// عرض الصفحة المناسبة
switch ($action) {
    case 'add':
        // نموذج إضافة لعبة جديدة
        ?>
        <div class="card">
            <div class="card-header">
                <h5>Add New Game</h5>
            </div>
            <div class="card-body">
                <form method="post" action="" enctype="multipart/form-data">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="id">Game ID</label>
                                <input type="text" id="id" name="id" class="form-control" required>
                                <small class="form-text text-muted">Unique identifier for the game (e.g., pubg, fortnite)</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="name">Game Name</label>
                                <input type="text" id="name" name="name" class="form-control" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="currency">Currency Name</label>
                                <input type="text" id="currency" name="currency" class="form-control">
                                <small class="form-text text-muted">Name of the in-game currency (e.g., UC, V-Bucks)</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="backgroundColor">Background Color</label>
                                <input type="color" id="backgroundColor" name="backgroundColor" class="form-control" value="#1e88e5">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="currency_amounts">Currency Amounts</label>
                        <input type="text" id="currency_amounts" name="currency_amounts" class="form-control" placeholder="600, 1200, 1600, 2400, 3000">
                        <small class="form-text text-muted">Enter currency amounts separated by commas (e.g., 600, 1200, 1600). These will be the options users can choose from.</small>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="image">Game Image</label>
                                <div class="mb-2">
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="image_type" id="image_type_file" value="file" checked onchange="toggleImageInputs()">
                                        <label class="form-check-label" for="image_type_file">Upload File</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="image_type" id="image_type_url" value="url" onchange="toggleImageInputs()">
                                        <label class="form-check-label" for="image_type_url">Use URL</label>
                                    </div>
                                </div>
                                <div id="image_file_container">
                                    <input type="file" id="image" name="image" class="form-control">
                                    <small class="form-text text-muted">Recommended size: 300x300 pixels</small>
                                </div>
                                <div id="image_url_container" style="display: none;">
                                    <input type="url" id="image_url" name="image_url" class="form-control" placeholder="https://example.com/image.jpg">
                                    <small class="form-text text-muted">Enter direct URL to image</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="currencyIcon">Currency Icon</label>
                                <div class="mb-2">
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="currency_icon_type" id="currency_icon_type_file" value="file" checked onchange="toggleCurrencyIconInputs()">
                                        <label class="form-check-label" for="currency_icon_type_file">Upload File</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="currency_icon_type" id="currency_icon_type_url" value="url" onchange="toggleCurrencyIconInputs()">
                                        <label class="form-check-label" for="currency_icon_type_url">Use URL</label>
                                    </div>
                                </div>
                                <div id="currency_icon_file_container">
                                    <input type="file" id="currencyIcon" name="currencyIcon" class="form-control">
                                    <small class="form-text text-muted">Recommended size: 50x50 pixels</small>
                                </div>
                                <div id="currency_icon_url_container" style="display: none;">
                                    <input type="url" id="currency_icon_url" name="currency_icon_url" class="form-control" placeholder="https://example.com/icon.png">
                                    <small class="form-text text-muted">Enter direct URL to icon</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mt-3">
                        <label for="last_update">Last Update</label>
                        <select id="last_update" name="last_update" class="form-control">
                            <option value="1 minute ago">1 minute ago</option>
                            <option value="5 minutes ago">5 minutes ago</option>
                            <option value="10 minutes ago">10 minutes ago</option>
                            <option value="30 minutes ago">30 minutes ago</option>
                            <option value="1 hour ago" selected>1 hour ago</option>
                            <option value="2 hours ago">2 hours ago</option>
                            <option value="3 hours ago">3 hours ago</option>
                            <option value="6 hours ago">6 hours ago</option>
                            <option value="12 hours ago">12 hours ago</option>
                            <option value="1 day ago">1 day ago</option>
                            <option value="2 days ago">2 days ago</option>
                            <option value="3 days ago">3 days ago</option>
                            <option value="1 week ago">1 week ago</option>
                        </select>
                        <small class="form-text text-muted">Select when the game was last updated</small>
                    </div>

                    <div class="card mt-4 mb-4">
                        <div class="card-header">
                            <h5>CPA Settings</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="cpa_script">CPA Script</label>
                                        <textarea id="cpa_script" name="cpa_script" class="form-control" rows="4"></textarea>
                                        <small class="form-text text-muted">Enter the CPA script code that will be added to the game page</small>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="cpa_function">CPA Function Name</label>
                                        <input type="text" id="cpa_function" name="cpa_function" class="form-control">
                                        <small class="form-text text-muted">Enter the function name that will be called to trigger the CPA offer (e.g., _Vx)</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-check mt-3">
                        <input type="checkbox" id="available" name="available" class="form-check-input" checked>
                        <label for="available" class="form-check-label">Available</label>
                        <small class="form-text text-muted d-block">If checked, the game will be visible on the website</small>
                    </div>

                    <div class="mt-4">
                        <button type="submit" class="btn btn-primary">Add Game</button>
                        <a href="games.php" class="btn btn-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
        <?php
        break;

    case 'edit':
        // نموذج تحديث اللعبة
        ?>
        <div class="card">
            <div class="card-header">
                <h5>Edit Game: <?php echo $game['name']; ?></h5>
            </div>
            <div class="card-body">
                <form method="post" action="" enctype="multipart/form-data">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="id">Game ID</label>
                                <input type="text" id="id" class="form-control" value="<?php echo $game['id']; ?>" disabled>
                                <small class="form-text text-muted">Game ID cannot be changed</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="name">Game Name</label>
                                <input type="text" id="name" name="name" class="form-control" value="<?php echo $game['name']; ?>" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="currency">Currency Name</label>
                                <input type="text" id="currency" name="currency" class="form-control" value="<?php echo $game['currency'] ?? ''; ?>">
                                <small class="form-text text-muted">Name of the in-game currency (e.g., UC, V-Bucks)</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="backgroundColor">Background Color</label>
                                <input type="color" id="backgroundColor" name="backgroundColor" class="form-control" value="<?php echo $game['backgroundColor'] ?? '#1e88e5'; ?>">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="currency_amounts">Currency Amounts</label>
                        <input type="text" id="currency_amounts" name="currency_amounts" class="form-control"
                               value="<?php echo isset($game['currencyAmounts']) ? implode(', ', $game['currencyAmounts']) : '600, 1200, 1600'; ?>"
                               placeholder="600, 1200, 1600, 2400, 3000">
                        <small class="form-text text-muted">Enter currency amounts separated by commas (e.g., 600, 1200, 1600). These will be the options users can choose from.</small>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="image">Game Image</label>
                                <?php if (isset($game['image']) && !empty($game['image'])): ?>
                                <div class="mb-2">
                                    <img src="<?php echo $game['image']; ?>" alt="<?php echo $game['name']; ?>" style="max-width: 100px; max-height: 100px;">
                                </div>
                                <?php endif; ?>
                                <div class="mb-2">
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="image_type" id="image_type_file" value="file" checked onchange="toggleImageInputs()">
                                        <label class="form-check-label" for="image_type_file">Upload File</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="image_type" id="image_type_url" value="url" onchange="toggleImageInputs()">
                                        <label class="form-check-label" for="image_type_url">Use URL</label>
                                    </div>
                                </div>
                                <div id="image_file_container">
                                    <input type="file" id="image" name="image" class="form-control">
                                    <small class="form-text text-muted">Leave empty to keep current image</small>
                                </div>
                                <div id="image_url_container" style="display: none;">
                                    <input type="url" id="image_url" name="image_url" class="form-control" placeholder="https://example.com/image.jpg">
                                    <small class="form-text text-muted">Enter direct URL to image</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="currencyIcon">Currency Icon</label>
                                <?php if (isset($game['currencyIcon']) && !empty($game['currencyIcon'])): ?>
                                <div class="mb-2">
                                    <img src="<?php echo $game['currencyIcon']; ?>" alt="Currency Icon" style="max-width: 50px; max-height: 50px;">
                                </div>
                                <?php endif; ?>
                                <div class="mb-2">
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="currency_icon_type" id="currency_icon_type_file" value="file" checked onchange="toggleCurrencyIconInputs()">
                                        <label class="form-check-label" for="currency_icon_type_file">Upload File</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="currency_icon_type" id="currency_icon_type_url" value="url" onchange="toggleCurrencyIconInputs()">
                                        <label class="form-check-label" for="currency_icon_type_url">Use URL</label>
                                    </div>
                                </div>
                                <div id="currency_icon_file_container">
                                    <input type="file" id="currencyIcon" name="currencyIcon" class="form-control">
                                    <small class="form-text text-muted">Leave empty to keep current icon</small>
                                </div>
                                <div id="currency_icon_url_container" style="display: none;">
                                    <input type="url" id="currency_icon_url" name="currency_icon_url" class="form-control" placeholder="https://example.com/icon.png">
                                    <small class="form-text text-muted">Enter direct URL to icon</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mt-3">
                        <label for="last_update">Last Update</label>
                        <select id="last_update" name="last_update" class="form-control">
                            <option value="1 minute ago" <?php echo (isset($game['last_update']) && $game['last_update'] === '1 minute ago') ? 'selected' : ''; ?>>1 minute ago</option>
                            <option value="5 minutes ago" <?php echo (isset($game['last_update']) && $game['last_update'] === '5 minutes ago') ? 'selected' : ''; ?>>5 minutes ago</option>
                            <option value="10 minutes ago" <?php echo (isset($game['last_update']) && $game['last_update'] === '10 minutes ago') ? 'selected' : ''; ?>>10 minutes ago</option>
                            <option value="30 minutes ago" <?php echo (isset($game['last_update']) && $game['last_update'] === '30 minutes ago') ? 'selected' : ''; ?>>30 minutes ago</option>
                            <option value="1 hour ago" <?php echo (!isset($game['last_update']) || $game['last_update'] === '1 hour ago') ? 'selected' : ''; ?>>1 hour ago</option>
                            <option value="2 hours ago" <?php echo (isset($game['last_update']) && $game['last_update'] === '2 hours ago') ? 'selected' : ''; ?>>2 hours ago</option>
                            <option value="3 hours ago" <?php echo (isset($game['last_update']) && $game['last_update'] === '3 hours ago') ? 'selected' : ''; ?>>3 hours ago</option>
                            <option value="6 hours ago" <?php echo (isset($game['last_update']) && $game['last_update'] === '6 hours ago') ? 'selected' : ''; ?>>6 hours ago</option>
                            <option value="12 hours ago" <?php echo (isset($game['last_update']) && $game['last_update'] === '12 hours ago') ? 'selected' : ''; ?>>12 hours ago</option>
                            <option value="1 day ago" <?php echo (isset($game['last_update']) && $game['last_update'] === '1 day ago') ? 'selected' : ''; ?>>1 day ago</option>
                            <option value="2 days ago" <?php echo (isset($game['last_update']) && $game['last_update'] === '2 days ago') ? 'selected' : ''; ?>>2 days ago</option>
                            <option value="3 days ago" <?php echo (isset($game['last_update']) && $game['last_update'] === '3 days ago') ? 'selected' : ''; ?>>3 days ago</option>
                            <option value="1 week ago" <?php echo (isset($game['last_update']) && $game['last_update'] === '1 week ago') ? 'selected' : ''; ?>>1 week ago</option>
                        </select>
                        <small class="form-text text-muted">Select when the game was last updated</small>
                    </div>

                    <div class="card mt-4 mb-4">
                        <div class="card-header">
                            <h5>CPA Settings</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="cpa_script">CPA Script</label>
                                        <textarea id="cpa_script" name="cpa_script" class="form-control" rows="4"><?php echo htmlspecialchars($game['cpa_script'] ?? ''); ?></textarea>
                                        <small class="form-text text-muted">Enter the CPA script code that will be added to the game page</small>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="cpa_function">CPA Function Name</label>
                                        <input type="text" id="cpa_function" name="cpa_function" class="form-control" value="<?php echo htmlspecialchars($game['cpa_function'] ?? ''); ?>">
                                        <small class="form-text text-muted">Enter the function name that will be called to trigger the CPA offer (e.g., _Vx)</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-check mt-3">
                        <input type="checkbox" id="available" name="available" class="form-check-input" <?php echo (isset($game['available']) && $game['available'] === true) ? 'checked' : ''; ?>>
                        <label for="available" class="form-check-label">Available</label>
                        <small class="form-text text-muted d-block">If checked, the game will be visible on the website</small>
                    </div>

                    <div class="mt-4">
                        <button type="submit" class="btn btn-primary">Update Game</button>
                        <a href="games.php" class="btn btn-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
        <?php
        break;

    default:
        // قائمة الألعاب
        $games = getGames();
        ?>
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-gamepad me-2"></i>Games Management</h5>
                <a href="games.php?action=add" class="btn btn-primary">
                    <i class="fas fa-plus-circle me-2"></i> Add New Game
                </a>
            </div>
            <div class="card-body">
                <?php if (empty($games)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>No games found. Click the button above to add a new game.
                </div>
                <?php else: ?>
                <div class="games-filter mb-4">
                    <div class="row align-items-center">
                        <div class="col-md-6 mb-3 mb-md-0">
                            <div class="input-group">
                                <input type="text" id="gameSearch" class="form-control" placeholder="Search games...">
                                <button class="btn btn-outline-secondary" type="button">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-primary active" data-filter="all">All</button>
                                <button type="button" class="btn btn-outline-success" data-filter="active">Active</button>
                                <button type="button" class="btn btn-outline-danger" data-filter="inactive">Inactive</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="games-table table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Image</th>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Currency</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($games as $game): ?>
                            <tr class="game-row <?php echo (isset($game['available']) && $game['available'] === true) ? 'status-active' : 'status-inactive'; ?>">
                                <td>
                                    <img src="<?php echo $game['image'] ?? '../img/placeholder-game.svg'; ?>" alt="<?php echo $game['name']; ?>" class="game-image">
                                </td>
                                <td><?php echo $game['id']; ?></td>
                                <td>
                                    <strong><?php echo $game['name']; ?></strong>
                                    <?php if (isset($game['last_update'])): ?>
                                    <div class="small text-muted">Updated: <?php echo $game['last_update']; ?></div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (isset($game['currencyIcon']) && !empty($game['currencyIcon'])): ?>
                                    <div class="d-flex align-items-center">
                                        <img src="<?php echo $game['currencyIcon']; ?>" alt="Currency" style="width: 20px; height: 20px; margin-right: 5px;">
                                        <?php echo $game['currency'] ?? 'N/A'; ?>
                                    </div>
                                    <?php else: ?>
                                    <?php echo $game['currency'] ?? 'N/A'; ?>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (isset($game['available']) && $game['available'] === true): ?>
                                    <span class="badge bg-success">Active</span>
                                    <?php else: ?>
                                    <span class="badge bg-danger">Inactive</span>
                                    <?php endif; ?>
                                </td>
                                <td class="game-actions">
                                    <div class="btn-group">
                                        <a href="../game.php?id=<?php echo $game['id']; ?>" class="btn btn-sm btn-info" target="_blank" title="View Game">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="games.php?action=edit&id=<?php echo $game['id']; ?>" class="btn btn-sm btn-warning" title="Edit Game">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="games.php?action=delete&id=<?php echo $game['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this game?');" title="Delete Game">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php
        break;
}

// إضافة سكريبت JavaScript للتبديل بين حقول الملف والرابط والبحث والتصفية
$extraScripts = <<<EOT
<script>
    function toggleImageInputs() {
        const fileContainer = document.getElementById('image_file_container');
        const urlContainer = document.getElementById('image_url_container');
        const fileRadio = document.getElementById('image_type_file');

        if (fileContainer && urlContainer && fileRadio) {
            if (fileRadio.checked) {
                fileContainer.style.display = 'block';
                urlContainer.style.display = 'none';
            } else {
                fileContainer.style.display = 'none';
                urlContainer.style.display = 'block';
            }
        }
    }

    function toggleCurrencyIconInputs() {
        const fileContainer = document.getElementById('currency_icon_file_container');
        const urlContainer = document.getElementById('currency_icon_url_container');
        const fileRadio = document.getElementById('currency_icon_type_file');

        if (fileContainer && urlContainer && fileRadio) {
            if (fileRadio.checked) {
                fileContainer.style.display = 'block';
                urlContainer.style.display = 'none';
            } else {
                fileContainer.style.display = 'none';
                urlContainer.style.display = 'block';
            }
        }
    }

    // تنفيذ الدوال عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        // تبديل حقول الملف والرابط
        toggleImageInputs();
        toggleCurrencyIconInputs();

        // وظائف البحث والتصفية
        const gameSearch = document.getElementById('gameSearch');
        const filterButtons = document.querySelectorAll('[data-filter]');
        const gameRows = document.querySelectorAll('.game-row');

        // وظيفة البحث
        if (gameSearch) {
            gameSearch.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();

                gameRows.forEach(row => {
                    const gameName = row.querySelector('td:nth-child(3)').textContent.toLowerCase();
                    const gameId = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
                    const currentFilter = document.querySelector('[data-filter].active').getAttribute('data-filter');

                    // التحقق من تطابق البحث
                    const matchesSearch = gameName.includes(searchTerm) || gameId.includes(searchTerm);

                    // التحقق من تطابق التصفية
                    let matchesFilter = true;
                    if (currentFilter === 'active') {
                        matchesFilter = row.classList.contains('status-active');
                    } else if (currentFilter === 'inactive') {
                        matchesFilter = row.classList.contains('status-inactive');
                    }

                    // إظهار أو إخفاء الصف بناءً على البحث والتصفية
                    row.style.display = (matchesSearch && matchesFilter) ? '' : 'none';
                });
            });
        }

        // وظيفة التصفية
        if (filterButtons.length > 0) {
            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // إزالة الفئة النشطة من جميع الأزرار
                    filterButtons.forEach(btn => btn.classList.remove('active'));

                    // إضافة الفئة النشطة للزر المحدد
                    this.classList.add('active');

                    const filter = this.getAttribute('data-filter');
                    const searchTerm = gameSearch ? gameSearch.value.toLowerCase() : '';

                    gameRows.forEach(row => {
                        const gameName = row.querySelector('td:nth-child(3)').textContent.toLowerCase();
                        const gameId = row.querySelector('td:nth-child(2)').textContent.toLowerCase();

                        // التحقق من تطابق البحث
                        const matchesSearch = gameName.includes(searchTerm) || gameId.includes(searchTerm);

                        // التحقق من تطابق التصفية
                        let matchesFilter = true;
                        if (filter === 'active') {
                            matchesFilter = row.classList.contains('status-active');
                        } else if (filter === 'inactive') {
                            matchesFilter = row.classList.contains('status-inactive');
                        }

                        // إظهار أو إخفاء الصف بناءً على البحث والتصفية
                        row.style.display = (matchesSearch && matchesFilter) ? '' : 'none';
                    });
                });
            });
        }

        // تحسين مظهر الجدول
        const gamesTable = document.querySelector('.games-table');
        if (gamesTable) {
            // إضافة فئة للصفوف عند التحويم
            const tableRows = gamesTable.querySelectorAll('tbody tr');
            tableRows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.classList.add('row-hover');
                });
                row.addEventListener('mouseleave', function() {
                    this.classList.remove('row-hover');
                });
            });
        }
    });
</script>
EOT;

// تضمين ملف التذييل
include 'footer.php';
?>
