# دليل البدء السريع

## 🚀 تشغيل المشروع

### الطريقة الأولى - مباشرة
```bash
# افتح index.html في المتصفح مباشرة
```

### الطريقة الثانية - خادم محلي
```bash
# Python
python -m http.server 8000

# Node.js
npx serve .

# PHP (إذا كان متاح)
php -S localhost:8000
```

## ⚙️ تخصيص سريع

### تغيير اسم الموقع
```json
// في settings.json
{
    "site_name": "اسم موقعك الجديد"
}
```

### إضافة لعبة جديدة
```json
// في games.json
{
    "id": "game_id",
    "name": "اسم اللعبة",
    "currency": "اسم العملة",
    "backgroundColor": "#FF0000",
    "currencyAmounts": [100, 500, 1000],
    "available": true,
    "image": "img/placeholder-game.svg",
    "currencyIcon": "img/currency/default-currency.png"
}
```

### تغيير الألوان
```css
/* في styles.css أو game.css */
:root {
    --primary-color: #your-color;
}
```

## 📱 اختبار سريع
1. افتح `http://localhost:8000`
2. ابحث عن لعبة
3. اضغط START
4. أدخل اسم مستخدم وهمي
5. اختبر عملية التحميل

## 🌐 نشر سريع

### GitHub Pages
1. ارفع الملفات لـ GitHub
2. فعّل GitHub Pages
3. الموقع جاهز!

### Netlify
1. اسحب المجلد لـ Netlify
2. الموقع جاهز فوراً!

## 🔧 استكشاف الأخطاء

### الألعاب لا تظهر
- تحقق من ملف `games.json`
- تأكد من صحة JSON syntax

### الإعدادات لا تعمل  
- تحقق من ملف `settings.json`
- افتح Developer Tools (F12)

### الصور لا تظهر
- تحقق من مسارات الصور
- تأكد من وجود الملفات

## 📞 المساعدة
راجع `README_HTML_VERSION.md` للتوثيق الكامل
