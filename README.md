# Filora - Free Game Currency Generator

🎮 **مولد عملات الألعاب المجانية** - موقع HTML/CSS/JavaScript خالص يحتوي على 50 لعبة شائعة

## ✨ المميزات
- 🎯 **50 لعبة شائعة** مع كميات عملة مخصصة
- 📱 **تصميم متجاوب** يعمل على جميع الأجهزة  
- ⚡ **أداء سريع** بدون الحاجة لخادم PHP
- 🎨 **تصميم جذاب** مع تأثيرات تفاعلية
- 🔍 **بحث متقدم** في الألعاب
- 🌈 **ألوان مخصصة** لكل لعبة

## 🚀 التشغيل السريع
1. حمّل الملفات
2. افتح `index.html` في المتصفح
3. أو استخدم خادم محلي: `python -m http.server 8000`

## 📁 الملفات الرئيسية
- `index.html` - الصفحة الرئيسية
- `game.html` - صفحة اللعبة
- `games.json` - بيانات 50 لعبة
- `settings.json` - إعدادات الموقع
- `script.js` & `game.js` - الوظائف التفاعلية
- `styles.css` & `game.css` - التصميم

## 🎮 بعض الألعاب المتاحة
Roblox • PUBG Mobile • Free Fire • Fortnite • Call of Duty Mobile • Mobile Legends • Wild Rift • Clash of Clans • Minecraft • Genshin Impact • VALORANT • League of Legends • Among Us • FIFA Mobile • Apex Legends • وأكثر من 35 لعبة أخرى!

## ⚙️ التخصيص
- **الألعاب**: عدّل `games.json`
- **الإعدادات**: عدّل `settings.json`  
- **التصميم**: عدّل ملفات CSS

## 🌐 الاستضافة
يمكن استضافة المشروع مجاناً على:
- GitHub Pages
- Netlify
- Vercel
- Firebase Hosting

## 📄 الترخيص
مشروع مفتوح المصدر للاستخدام الشخصي والتعليمي

---
💡 **نصيحة**: راجع `README_HTML_VERSION.md` للتوثيق الكامل والتفصيلي
