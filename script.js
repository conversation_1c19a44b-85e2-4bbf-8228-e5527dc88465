// سنقوم بتحميل بيانات الألعاب والإعدادات من ملفات JSON
let games = [];
let settings = {};

// دالة لإنشاء بطاقات الألعاب
function createGameCards(gamesData) {
    const gameCardsContainer = document.getElementById('game-cards-container');
    gameCardsContainer.innerHTML = '';

    gamesData.forEach(game => {
        // تخطي الألعاب غير المتاحة إذا كانت الخاصية موجودة
        if (game.available === false) return;

        const gameCard = document.createElement('div');
        gameCard.className = 'game-card';

        // إضافة لون خلفية مخصص للصورة إذا كان موجوداً
        const bgColor = game.backgroundColor || '#1e88e5';

        // استخدام الصورة الافتراضية إذا لم تكن صورة اللعبة موجودة
        const imageSrc = game.image || 'img/placeholder-game.svg';

        // إضافة معلومات العملة إذا كانت موجودة
        const currencyInfo = game.currency ? `<div class="game-currency">${game.currency}</div>` : '';

        gameCard.innerHTML = `
            <div class="game-img-container" style="background-color: ${bgColor}">
                <img src="${imageSrc}" alt="${game.name}" class="game-img" onerror="this.src='img/placeholder-game.svg'">
            </div>
            <div class="game-name">${game.name}</div>
            ${currencyInfo}
            <button class="start-btn" onclick="startGame('${game.id}')">START</button>
        `;

        gameCardsContainer.appendChild(gameCard);
    });
}

// دالة للتعامل مع البحث
function handleSearch() {
    const searchInput = document.getElementById('search-input');
    const searchTerm = searchInput.value.toLowerCase();

    const filteredGames = games.filter(game => {
        // البحث في اسم اللعبة
        if (game.name.toLowerCase().includes(searchTerm)) return true;

        // البحث في اسم العملة إذا كانت موجودة
        if (game.currency && game.currency.toLowerCase().includes(searchTerm)) return true;

        return false;
    });

    createGameCards(filteredGames);
}

// دالة لبدء اللعبة
function startGame(gameId) {
    // البحث عن اللعبة بواسطة المعرف
    const game = games.find(g => g.id === gameId);

    if (game) {
        // توجيه المستخدم إلى صفحة اللعبة HTML
        window.location.href = `game.html?id=${gameId}`;
    } else {
        alert('اللعبة غير موجودة!');
    }
}

// دالة لتحميل الإعدادات من ملف JSON
async function loadSettings() {
    try {
        const response = await fetch('settings.json');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        settings = await response.json();
        updatePageWithSettings();
    } catch (error) {
        console.error('خطأ في تحميل الإعدادات:', error);
        // استخدام إعدادات افتراضية
        settings = {
            site_name: "Filora",
            site_logo: "img/rocket.svg",
            tagline: "Generating Currencies for Android and iOS.",
            main_title: "The First Site in Generating Currencies",
            search_title: "FIND YOUR APP",
            search_placeholder: "Search for apps..."
        };
        updatePageWithSettings();
    }
}

// دالة لتحديث الصفحة بالإعدادات
function updatePageWithSettings() {
    // تحديث عنوان الصفحة
    document.title = settings.site_name;
    document.getElementById('page-title').textContent = settings.site_name;

    // تحديث الشعار
    const logoImg = document.getElementById('rocket-img');
    if (logoImg) {
        logoImg.src = settings.site_logo;
        logoImg.alt = settings.site_name + ' Logo';
    }

    // تحديث اسم الموقع
    const siteName = document.getElementById('site-name');
    if (siteName) siteName.textContent = settings.site_name;

    // تحديث الشعار النصي
    const tagline = document.getElementById('tagline');
    if (tagline) tagline.textContent = settings.tagline;

    // تحديث العنوان الرئيسي
    const mainTitle = document.getElementById('main-title');
    if (mainTitle) mainTitle.textContent = settings.main_title;

    // تحديث عنوان البحث
    const searchTitle = document.getElementById('search-title');
    if (searchTitle) searchTitle.textContent = settings.search_title;

    // تحديث placeholder البحث
    const searchInput = document.getElementById('search-input');
    if (searchInput) searchInput.placeholder = settings.search_placeholder;

    // تحديث الوصف والكلمات المفتاحية
    if (settings.site_description) {
        document.getElementById('page-description').content = settings.site_description;
    }
    if (settings.site_keywords) {
        document.getElementById('page-keywords').content = settings.site_keywords;
    }
    if (settings.theme_color) {
        document.getElementById('theme-color').content = settings.theme_color;
    }
    if (settings.site_favicon) {
        document.getElementById('favicon').href = settings.site_favicon;
    }
}

// دالة لتحميل بيانات الألعاب من ملف JSON
async function loadGamesData() {
    try {
        const response = await fetch('games.json');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        games = data.games;
        createGameCards(games);
    } catch (error) {
        console.error('خطأ في تحميل بيانات الألعاب:', error);
    }
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', async () => {
    // تحميل الإعدادات أولاً
    await loadSettings();

    // تحميل بيانات الألعاب من ملف JSON
    await loadGamesData();

    // إعداد وظيفة البحث
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        searchInput.addEventListener('input', handleSearch);
    }
});
