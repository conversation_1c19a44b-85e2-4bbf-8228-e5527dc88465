# Filora - HTML Version

## نظرة عامة
مشروع مولد عملات الألعاب المجانية بتقنية HTML/CSS/JavaScript خالص بدون الحاجة لخادم PHP أو لوحة تحكم. يحتوي على 50 لعبة شائعة مع كميات عملة مخصصة لكل لعبة.

## الملفات الرئيسية

### ملفات HTML
- `index.html` - الصفحة الرئيسية
- `game.html` - صفحة اللعبة

### ملفات التكوين (JSON)
- `settings.json` - إعدادات الموقع
- `games.json` - بيانات الألعاب

### ملفات JavaScript
- `script.js` - سكريبت الصفحة الرئيسية
- `game.js` - سكريبت صفحة اللعبة

### ملفات CSS
- `styles.css` - تصميم الصفحة الرئيسية
- `game.css` - تصميم صفحة اللعبة

### مجلد الصور
- `img/` - يحتوي على جميع الصور والأيقونات



## الميزات

### ✅ الميزات الرئيسية:
- **50 لعبة شائعة** مع كميات عملة مخصصة
- **تصميم متجاوب** يعمل على جميع الأجهزة
- **تأثيرات تفاعلية** للتحميل والمعالجة
- **بحث متقدم** في الألعاب
- **ألوان مخصصة** لكل لعبة
- **كميات عملة متنوعة** لكل لعبة

### 🆕 المزايا التقنية:
- **لا حاجة لخادم PHP** - يعمل على أي خادم ويب
- **ملفات JSON بسيطة** للتكوين والإعدادات
- **سهولة التخصيص** والتعديل
- **أداء ممتاز** وسرعة تحميل
- **استضافة مجانية** على GitHub Pages وغيرها

## كيفية الاستخدام

### تشغيل المشروع:
1. افتح `index.html` في المتصفح مباشرة
2. أو استخدم خادم محلي بسيط:
   ```bash
   python -m http.server 8000
   # أو
   npx serve .
   ```

### تخصيص الإعدادات:
عدّل ملف `settings.json`:
```json
{
    "site_name": "اسم موقعك",
    "site_logo": "مسار الشعار",
    "tagline": "الشعار النصي",
    "main_title": "العنوان الرئيسي"
}
```

### إضافة/تعديل الألعاب:
عدّل ملف `games.json`:
```json
{
    "games": [
        {
            "id": "game_id",
            "name": "اسم اللعبة",
            "currency": "اسم العملة",
            "currencyAmounts": [500, 1000, 1500],
            "image": "مسار صورة اللعبة",
            "currencyIcon": "مسار أيقونة العملة",
            "backgroundColor": "#لون_الخلفية",
            "available": true
        }
    ]
}
```

## هيكل المجلدات
```
├── index.html          # الصفحة الرئيسية
├── game.html           # صفحة اللعبة
├── settings.json       # إعدادات الموقع
├── games.json          # بيانات 50 لعبة
├── script.js           # سكريبت الصفحة الرئيسية
├── game.js             # سكريبت صفحة اللعبة
├── styles.css          # تصميم الصفحة الرئيسية
├── game.css            # تصميم صفحة اللعبة
└── img/                # مجلد الصور والأيقونات
    ├── currency/       # أيقونات العملات
    ├── rocket.svg      # شعار الموقع
    ├── user-icon.svg   # أيقونة المستخدم
    └── ...
```

## قائمة الألعاب المتاحة (50 لعبة)
- Roblox, PUBG Mobile, Free Fire, Fortnite
- Call of Duty Mobile, Mobile Legends, Wild Rift
- Clash of Clans, Clash Royale, Minecraft
- Genshin Impact, Honkai Impact 3rd, Brawl Stars
- Hay Day, Candy Crush Saga, Pokémon GO
- FIFA Mobile, Apex Legends, VALORANT
- League of Legends, Among Us, Stumble Guys
- Fall Guys, Rocket League, Overwatch 2
- Hearthstone, Diablo Immortal, World of Warcraft
- Counter-Strike 2, Dota 2, Destiny 2
- Call of Duty Warzone, Battlefield 2042
- GTA V Online, Red Dead Online, FIFA 23
- NBA 2K24, Madden NFL 24, Warframe
- Paladins, SMITE, Heroes of the Storm
- StarCraft II, Teamfight Tactics
- Legends of Runeterra, Honkai Star Rail
- Tower of Fantasy, Wuthering Waves
- Arknights, Azur Lane, Epic Seven
- Guardian Tales, Summoners War

## المتطلبات
- متصفح ويب حديث
- لا حاجة لخادم PHP
- (اختياري) خادم ويب بسيط للتطوير

## الاستضافة
يمكن استضافة المشروع على أي خدمة استضافة ثابتة مثل:
- GitHub Pages
- Netlify
- Vercel
- Firebase Hosting
- أي خادم ويب عادي

## ملاحظات مهمة
- المشروع لا يحتاج لخادم PHP أو قاعدة بيانات
- جميع البيانات محفوظة في ملفات JSON
- يمكن تخصيص الألعاب والإعدادات بسهولة
- التصميم متجاوب ويدعم جميع الأجهزة
- يمكن استضافته مجاناً على GitHub Pages أو Netlify

## الدعم والتطوير
- لإضافة ألعاب جديدة: عدّل ملف `games.json`
- لتغيير الإعدادات: عدّل ملف `settings.json`
- لتخصيص التصميم: عدّل ملفات CSS
- للمساعدة: راجع التوثيق في الملف
