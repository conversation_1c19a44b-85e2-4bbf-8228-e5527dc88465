# 🎯 دليل سكريبت CPA

## 📋 نظرة عامة

تم تحديث النظام ليدعم تحميل سكريبت CPA بشكل ديناميكي من ملف `games.json`. هذا يسمح بتخصيص سكريبت CPA لكل لعبة على حدة.

## 🔧 كيفية العمل

### 1. تكوين سكريبت CPA في games.json

```json
{
    "id": "roblox",
    "name": "Roblox",
    "currency": "Robux",
    "cpa_script": "<script type=\"text/javascript\"> var BqnBB_UOm_eLXHqc={\"it\":4328674,\"key\":\"1566d\"}; </script> <script src=\"https://dlk457skl57zp.cloudfront.net/b45ef26.js\"></script>",
    "cpa_function": "_nS"
}
```

### 2. المتغيرات المطلوبة

- **cpa_script**: كود HTML للسكريبت الذي سيتم تحميله
- **cpa_function**: اسم الدالة التي سيتم استدعاؤها (مثل `_nS`)

### 3. آلية التحميل

1. عند تحميل صفحة اللعبة، يتم فحص وجود `cpa_script`
2. إذا كان موجوداً، يتم تحليل HTML وإضافة عناصر `<script>` إلى `<head>`
3. يتم تعيين متغير `hasCPA = true` و `cpaFunction` بالقيمة المحددة
4. عند النقر على خيار العملة، يتم تفعيل دالة CPA بعد 13 ثانية

## 🎮 سلوك اللعبة

### مع سكريبت CPA:
- التقدم يتوقف عند 87%
- رسالة "Verification Failed"
- تفعيل دالة CPA بعد 13 ثانية من النقر

### بدون سكريبت CPA:
- التقدم يصل إلى 100%
- رسالة نجاح
- لا يتم تفعيل أي دالة CPA

## 🧪 اختبار السكريبت

يمكنك استخدام ملف `cpa-test.html` لاختبار سكريبت CPA:

```bash
# فتح صفحة الاختبار
http://localhost:8000/cpa-test.html
```

## 📝 الألعاب المُفعّلة حالياً

- **Roblox**: مع سكريبت CPA
- **PUBG Mobile**: مع سكريبت CPA  
- **Honkai Impact 3rd**: مع سكريبت CPA
- **باقي الألعاب**: بدون سكريبت CPA (رسالة نجاح)

## 🔍 استكشاف الأخطاء

### فحص وجود دالة CPA:
```javascript
console.log(typeof window._nS); // should be 'function'
```

### فحص تحميل السكريبت:
```javascript
console.log(hasCPA); // should be true
console.log(cpaFunction); // should be '_nS'
```

### اختبار دالة CPA:
```javascript
if (typeof window._nS === 'function') {
    window._nS();
}
```

## ⚙️ إعدادات متقدمة

### تخصيص دالة CPA مختلفة:
```json
{
    "cpa_script": "<script src=\"https://example.com/custom-cpa.js\"></script>",
    "cpa_function": "_customFunction"
}
```

### إضافة متغيرات إضافية:
```json
{
    "cpa_script": "<script> var customVar = 'value'; </script> <script src=\"https://example.com/cpa.js\"></script>",
    "cpa_function": "_nS"
}
```

## 🚀 التحديثات الجديدة

1. **تحميل ديناميكي**: السكريبت يُحمّل من JSON وليس مُدمج في HTML
2. **اختبار تلقائي**: فحص توفر دالة CPA بعد التحميل
3. **معالجة أخطاء محسّنة**: تسجيل مفصل للأخطاء
4. **مرونة في التكوين**: إمكانية تخصيص دالة CPA لكل لعبة

## 📞 الدعم

إذا واجهت مشاكل مع سكريبت CPA:

1. تحقق من console للأخطاء
2. استخدم صفحة الاختبار `cpa-test.html`
3. تأكد من صحة تنسيق JSON في `games.json`
4. تحقق من توفر دالة CPA في النافذة العامة
