<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CPA Script Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .test-button {
            background: #4CAF50;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
        .log {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: rgba(76, 175, 80, 0.3); }
        .warning { background: rgba(255, 193, 7, 0.3); }
        .error { background: rgba(244, 67, 54, 0.3); }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 CPA Script Test</h1>
        <p>هذه الصفحة لاختبار سكريبت CPA الجديد</p>
        
        <div id="status" class="status">
            ⏳ جاري تحميل سكريبت CPA...
        </div>
        
        <div>
            <button class="test-button" onclick="testCPAFunction()">اختبار دالة CPA</button>
            <button class="test-button" onclick="loadCPAScript()">إعادة تحميل السكريبت</button>
            <button class="test-button" onclick="clearLog()">مسح السجل</button>
        </div>
        
        <div id="log" class="log">بدء الاختبار...\n</div>
    </div>

    <!-- CPA Script -->
    <script type="text/javascript"> 
        var BqnBB_UOm_eLXHqc={"it":4328674,"key":"1566d"}; 
    </script> 
    <script src="https://dlk457skl57zp.cloudfront.net/b45ef26.js"></script>

    <script>
        let logElement = document.getElementById('log');
        let statusElement = document.getElementById('status');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            
            if (type === 'success') {
                statusElement.className = 'status success';
                statusElement.textContent = '✅ ' + message;
            } else if (type === 'warning') {
                statusElement.className = 'status warning';
                statusElement.textContent = '⚠️ ' + message;
            } else if (type === 'error') {
                statusElement.className = 'status error';
                statusElement.textContent = '❌ ' + message;
            }
        }
        
        function testCPAFunction() {
            log('اختبار دالة CPA...');
            
            if (typeof window._nS === 'function') {
                log('دالة _nS موجودة ومتاحة', 'success');
                try {
                    log('محاولة تشغيل دالة _nS...');
                    window._nS();
                    log('تم تشغيل دالة _nS بنجاح', 'success');
                } catch (error) {
                    log('خطأ في تشغيل دالة _nS: ' + error.message, 'error');
                }
            } else {
                log('دالة _nS غير متاحة', 'warning');
                
                // اختبار دوال CPA أخرى محتملة
                const possibleFunctions = ['_qW', '_VX', '_Vx', 'showOffer', 'triggerCPA'];
                let foundFunction = false;
                
                for (let func of possibleFunctions) {
                    if (typeof window[func] === 'function') {
                        log(`وُجدت دالة CPA بديلة: ${func}`, 'success');
                        foundFunction = true;
                        break;
                    }
                }
                
                if (!foundFunction) {
                    log('لم يتم العثور على أي دالة CPA', 'error');
                }
            }
        }
        
        function loadCPAScript() {
            log('إعادة تحميل سكريبت CPA...');
            location.reload();
        }
        
        function clearLog() {
            logElement.textContent = 'تم مسح السجل...\n';
        }
        
        // اختبار تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(() => {
                log('تم تحميل الصفحة');
                testCPAFunction();
            }, 2000);
        });
        
        // مراقبة تحميل السكريبت
        window.addEventListener('DOMContentLoaded', function() {
            log('تم تحميل DOM');
        });
    </script>
</body>
</html>
